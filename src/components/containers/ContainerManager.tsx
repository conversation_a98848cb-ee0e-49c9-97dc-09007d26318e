'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import { GlassCard, AnimatedButton } from '@/components/ui/enhanced-card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Plus,
  Play,
  Square,
  RotateCcw,
  Trash2,
  Terminal,
  FileText,
  Activity,
  Clock,
  Cpu,
  HardDrive,
  Network,
  MoreVertical,
  Search,
  Filter,
  Download,
  Upload,
  Settings,
  Eye,
  EyeOff,
  Pause,
  RefreshCw,
  Container,
  Image as ImageIcon,
  Globe,
  Shield,
  Zap,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader2
} from 'lucide-react';
import { useDocker } from '@/hooks/useDocker';
import { ContainerInfo, CreateContainerOptions } from '@/services/docker';

interface ContainerManagerProps {
  onSelectContainer?: (container: ContainerInfo) => void;
}

export const ContainerManager: React.FC<ContainerManagerProps> = ({ onSelectContainer }) => {
  const {
    containers,
    loading,
    error,
    systemInfo,
    connected,
    refreshContainers,
    createContainer,
    startContainer,
    stopContainer,
    restartContainer,
    removeContainer,
    getContainerLogs,
    getContainerStats,
    execInContainer,
    pullImage,
    getSystemInfo
  } = useDocker();

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedContainer, setSelectedContainer] = useState<ContainerInfo | null>(null);
  const [showLogs, setShowLogs] = useState(false);
  const [showStats, setShowStats] = useState(false);
  const [containerToDelete, setContainerToDelete] = useState<string | null>(null);
  const [containerStats, setContainerStats] = useState<any>(null);
  const [containerLogs, setContainerLogs] = useState<string>('');
  const [refreshing, setRefreshing] = useState(false);

  const [newContainer, setNewContainer] = useState<CreateContainerOptions>({
    name: '',
    image: '',
    cpu: 1,
    memory: 512,
    ports: {},
    environment: {},
    volumes: {},
    networkMode: 'bridge',
    cmd: [],
    workingDir: '',
    user: '',
    privileged: false,
    autoRemove: false
  });

  // Filter containers based on search and status
  const filteredContainers = containers.filter(container => {
    const matchesSearch = container.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         container.image.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || container.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Get status statistics
  const statusStats = {
    total: containers.length,
    running: containers.filter(c => c.status === 'running').length,
    stopped: containers.filter(c => c.status === 'stopped').length,
    paused: containers.filter(c => c.status === 'paused').length,
    restarting: containers.filter(c => c.status === 'restarting').length
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refreshContainers();
      await getSystemInfo();
    } finally {
      setRefreshing(false);
    }
  };

  const handleCreateContainer = async () => {
    try {
      await createContainer(newContainer);
      setIsCreateDialogOpen(false);
      setNewContainer({
        name: '',
        image: '',
        cpu: 1,
        memory: 512,
        ports: {},
        environment: {},
        volumes: {},
        networkMode: 'bridge',
        cmd: [],
        workingDir: '',
        user: '',
        privileged: false,
        autoRemove: false
      });
    } catch (error) {
      console.error('Failed to create container:', error);
    }
  };

  const handleContainerAction = async (action: string, containerId: string) => {
    try {
      switch (action) {
        case 'start':
          await startContainer(containerId);
          break;
        case 'stop':
          await stopContainer(containerId);
          break;
        case 'restart':
          await restartContainer(containerId);
          break;
        case 'remove':
          await removeContainer(containerId);
          setContainerToDelete(null);
          break;
      }
    } catch (error) {
      console.error(`Failed to ${action} container:`, error);
    }
  };

  const handleShowLogs = async (container: ContainerInfo) => {
    setSelectedContainer(container);
    try {
      const logs = await getContainerLogs(container.id);
      setContainerLogs(logs);
      setShowLogs(true);
    } catch (error) {
      console.error('Failed to get container logs:', error);
    }
  };

  const handleShowStats = async (container: ContainerInfo) => {
    setSelectedContainer(container);
    try {
      const stats = await getContainerStats(container.id);
      setContainerStats(stats);
      setShowStats(true);
    } catch (error) {
      console.error('Failed to get container stats:', error);
    }
  };

  const getStatusColor = (status: ContainerInfo['status']) => {
    switch (status) {
      case 'running': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'stopped': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      case 'paused': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      case 'restarting': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20';
      case 'starting': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20';
      case 'stopping': return 'text-purple-600 bg-purple-100 dark:bg-purple-900/20';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
    }
  };

  const getStatusIcon = (status: ContainerInfo['status']) => {
    switch (status) {
      case 'running': return <CheckCircle className="h-4 w-4" />;
      case 'stopped': return <XCircle className="h-4 w-4" />;
      case 'paused': return <Pause className="h-4 w-4" />;
      case 'restarting': return <RotateCcw className="h-4 w-4" />;
      case 'starting': return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'stopping': return <Loader2 className="h-4 w-4 animate-spin" />;
      default: return <AlertTriangle className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Container Management</h2>
          <p className="text-muted-foreground">
            Manage your Docker containers with advanced monitoring and control
          </p>
        </div>
        <div className="flex items-center gap-3">
          <AnimatedButton
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing}
            loading={refreshing}
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </AnimatedButton>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <AnimatedButton>
                <Plus className="h-4 w-4" />
                Create Container
              </AnimatedButton>
            </DialogTrigger>
          </Dialog>
        </div>
      </div>

      {/* Connection Status & System Info */}
      {!connected && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-destructive/10 border border-destructive/20 rounded-lg p-4"
        >
          <div className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-5 w-5" />
            <span className="font-medium">Docker Connection Failed</span>
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            Unable to connect to Docker daemon. Please ensure Docker is running.
          </p>
        </motion.div>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <GlassCard variant="glass" animate className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Container className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="text-2xl font-bold">{statusStats.total}</p>
              <p className="text-sm text-muted-foreground">Total</p>
            </div>
          </div>
        </GlassCard>

        <GlassCard variant="glass" animate className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-500/10 rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-green-600">{statusStats.running}</p>
              <p className="text-sm text-muted-foreground">Running</p>
            </div>
          </div>
        </GlassCard>

        <GlassCard variant="glass" animate className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-red-500/10 rounded-lg">
              <XCircle className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-red-600">{statusStats.stopped}</p>
              <p className="text-sm text-muted-foreground">Stopped</p>
            </div>
          </div>
        </GlassCard>

        <GlassCard variant="glass" animate className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-yellow-500/10 rounded-lg">
              <Pause className="h-5 w-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-yellow-600">{statusStats.paused}</p>
              <p className="text-sm text-muted-foreground">Paused</p>
            </div>
          </div>
        </GlassCard>

        <GlassCard variant="glass" animate className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-500/10 rounded-lg">
              <RotateCcw className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-blue-600">{statusStats.restarting}</p>
              <p className="text-sm text-muted-foreground">Restarting</p>
            </div>
          </div>
        </GlassCard>
      </div>

      {/* Search and Filter Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search containers by name or image..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="min-w-[120px]">
              <Filter className="h-4 w-4 mr-2" />
              {statusFilter === 'all' ? 'All Status' : statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setStatusFilter('all')}>
              All Status
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setStatusFilter('running')}>
              Running
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setStatusFilter('stopped')}>
              Stopped
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setStatusFilter('paused')}>
              Paused
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setStatusFilter('restarting')}>
              Restarting
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="text-lg">Loading containers...</span>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-destructive/10 border border-destructive/20 rounded-lg p-6 text-center"
        >
          <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-destructive mb-2">Error Loading Containers</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <AnimatedButton onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </AnimatedButton>
        </motion.div>
      )}

      {/* Empty State */}
      {!loading && !error && filteredContainers.length === 0 && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center py-12"
        >
          <Container className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">
            {containers.length === 0 ? 'No Containers Found' : 'No Matching Containers'}
          </h3>
          <p className="text-muted-foreground mb-6">
            {containers.length === 0
              ? 'Get started by creating your first container'
              : 'Try adjusting your search or filter criteria'
            }
          </p>
          {containers.length === 0 && (
            <AnimatedButton onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Container
            </AnimatedButton>
          )}
        </motion.div>
      )}

      {/* Container Grid */}
      {!loading && !error && filteredContainers.length > 0 && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <AnimatePresence mode="popLayout">
            {filteredContainers.map((container, index) => (
              <motion.div
                key={container.id}
                layout
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <GlassCard variant="glass" animate className="h-full">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <CardTitle className="text-lg font-semibold truncate">
                          {container.name}
                        </CardTitle>
                        <p className="text-sm text-muted-foreground truncate">
                          {container.image}
                        </p>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          {container.status === 'stopped' && (
                            <DropdownMenuItem onClick={() => handleContainerAction('start', container.id)}>
                              <Play className="h-4 w-4 mr-2" />
                              Start
                            </DropdownMenuItem>
                          )}
                          {container.status === 'running' && (
                            <>
                              <DropdownMenuItem onClick={() => handleContainerAction('stop', container.id)}>
                                <Square className="h-4 w-4 mr-2" />
                                Stop
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleContainerAction('restart', container.id)}>
                                <RotateCcw className="h-4 w-4 mr-2" />
                                Restart
                              </DropdownMenuItem>
                            </>
                          )}
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleShowLogs(container)}>
                            <FileText className="h-4 w-4 mr-2" />
                            View Logs
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleShowStats(container)}>
                            <Activity className="h-4 w-4 mr-2" />
                            View Stats
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => onSelectContainer?.(container)}>
                            <Terminal className="h-4 w-4 mr-2" />
                            Terminal
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => setContainerToDelete(container.id)}
                            className="text-destructive focus:text-destructive"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Remove
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    <div className="flex items-center gap-2 mt-3">
                      <Badge className={`${getStatusColor(container.status)} border-0`}>
                        {getStatusIcon(container.status)}
                        <span className="ml-1 capitalize">{container.status}</span>
                      </Badge>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Resource Information */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <Cpu className="h-4 w-4 text-muted-foreground" />
                        <span>{container.cpu || 'N/A'} CPU</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <HardDrive className="h-4 w-4 text-muted-foreground" />
                        <span>{container.memory ? `${container.memory}MB` : 'N/A'}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Network className="h-4 w-4 text-muted-foreground" />
                        <span>{container.networkMode || 'bridge'}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>{container.created.toLocaleDateString()}</span>
                      </div>
                    </div>

                    {/* Port Mappings */}
                    {Object.keys(container.ports).length > 0 && (
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Port Mappings</p>
                        <div className="flex flex-wrap gap-1">
                          {Object.entries(container.ports).map(([containerPort, hostPort]) => (
                            <Badge key={containerPort} variant="outline" className="text-xs">
                              {hostPort}:{containerPort}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Quick Actions */}
                    <div className="flex gap-2 pt-2">
                      {container.status === 'stopped' && (
                        <AnimatedButton
                          size="sm"
                          onClick={() => handleContainerAction('start', container.id)}
                          className="flex-1"
                        >
                          <Play className="h-4 w-4 mr-1" />
                          Start
                        </AnimatedButton>
                      )}

                      {container.status === 'running' && (
                        <>
                          <AnimatedButton
                            size="sm"
                            onClick={() => onSelectContainer?.(container)}
                            className="flex-1"
                          >
                            <Terminal className="h-4 w-4 mr-1" />
                            Connect
                          </AnimatedButton>
                          <AnimatedButton
                            size="sm"
                            variant="outline"
                            onClick={() => handleContainerAction('stop', container.id)}
                          >
                            <Square className="h-4 w-4" />
                          </AnimatedButton>
                        </>
                      )}

                      {(container.status === 'starting' || container.status === 'stopping') && (
                        <AnimatedButton size="sm" disabled className="flex-1">
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          {container.status === 'starting' ? 'Starting...' : 'Stopping...'}
                        </AnimatedButton>
                      )}
                    </div>
                  </CardContent>
                </GlassCard>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      )}
    </div>
  );
};
